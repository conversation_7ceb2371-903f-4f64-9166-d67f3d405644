using UnityEngine;
public class CameraPan : MonoBehaviour
{
    [Tooltip("How fast the camera pans")]
    public float panSpeed = 0.5f;

    private void Update()
    {
        float x = Input.GetAxis("Horizontal");
        float y = Input.GetAxis("Vertical");

        if (Mathf.Abs(x) > 0f || Mathf.Abs(y) > 0f)
        {
            Vector3 move = new Vector3(x, y, 0f) * panSpeed * Time.deltaTime;
            transform.Translate(move, Space.Self);
        }
    }
}
