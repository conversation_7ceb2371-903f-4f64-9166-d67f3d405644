using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;
using StarterAssets;
using UnityEngine.InputSystem;

/// <summary>
/// Builds a simple pause menu and HUD at runtime. Removes the old Canvas from TestScene.
/// </summary>
public class GameUIManager : MonoBehaviour
{
    public SaveLoadManager saveLoadManager;
    public PlacementController placementController;
    public PlayerStamina playerStamina;
    public DefaultInputActions defaultInputActions;

    private GameObject pauseMenu;
    private Canvas canvas;

    private void Awake()
    {
        // Ensure EventSystem exists
        if (EventSystem.current == null)
        {
            var esGO = new GameObject("EventSystem",
                typeof(EventSystem),
                typeof(StandaloneInputModule));
            DontDestroyOnLoad(esGO);
        }

        if (saveLoadManager == null)
            saveLoadManager = UnityEngine.Object.FindFirstObjectByType<SaveLoadManager>();
        if (placementController == null)
            placementController = UnityEngine.Object.FindFirstObjectByType<PlacementController>();

        var canvasGO = new GameObject("GameUI");
        // Set to UI layer (default Unity UI layer is 5)
        canvasGO.layer = LayerMask.NameToLayer("UI");
        canvas = canvasGO.AddComponent<Canvas>();
        canvas.renderMode = RenderMode.ScreenSpaceOverlay;
        canvas.additionalShaderChannels = AdditionalCanvasShaderChannels.TexCoord1 | AdditionalCanvasShaderChannels.TexCoord2 | AdditionalCanvasShaderChannels.TexCoord3;
        var scaler = canvas.gameObject.AddComponent<CanvasScaler>();
        scaler.uiScaleMode = CanvasScaler.ScaleMode.ScaleWithScreenSize;
        scaler.referenceResolution = new Vector2(1920, 1080);
        scaler.screenMatchMode = CanvasScaler.ScreenMatchMode.MatchWidthOrHeight;
        scaler.matchWidthOrHeight = 0.5f;
        canvas.gameObject.AddComponent<GraphicRaycaster>();
        // Ensure the Canvas fills the screen for proper UI interaction
        var canvasRect = canvas.GetComponent<RectTransform>();
        if (canvasRect != null)
        {
            canvasRect.anchorMin = Vector2.zero;
            canvasRect.anchorMax = Vector2.one;
            canvasRect.offsetMin = Vector2.zero;
            canvasRect.offsetMax = Vector2.zero;
        }

        if (playerStamina == null)
        {
            playerStamina = UnityEngine.Object.FindFirstObjectByType<PlayerStamina>();
            if (playerStamina == null)
            {
                var controller = UnityEngine.Object.FindFirstObjectByType<ThirdPersonController>();
                if (controller != null)
                {
                    playerStamina = controller.gameObject.AddComponent<PlayerStamina>();
                }
            }
        }

        BuildHUD();
        BuildPauseMenu();
        pauseMenu.SetActive(false);
        SetPauseState(false);
    }

    private void Update()
    {
        // Always allow Escape to toggle pause
        if (Input.GetKeyDown(KeyCode.LeftControl))
        {
            TogglePause();
        }

        // Toggle Building Mode with 'B' key
        if (Input.GetKeyDown(KeyCode.B))
        {
            Debug.Log("Building Mode toggled via 'B' key");
            if (placementController != null)
                placementController.TogglePlacementMode();
        }
    }

    private void BuildHUD()
    {
        var hud = new GameObject("HUD");
        hud.layer = LayerMask.NameToLayer("UI");
        hud.transform.SetParent(canvas.transform, false);

        // Placement button removed. Use key press for Building Mode instead.

        var staminaObj = CreateSlider(new Vector2(160, 15));
        staminaObj.layer = LayerMask.NameToLayer("UI");
        staminaObj.transform.SetParent(hud.transform, false);
        var sRect = staminaObj.GetComponent<RectTransform>();
        sRect.anchorMin = new Vector2(0.5f, 0f);
        sRect.anchorMax = new Vector2(0.5f, 0f);
        sRect.pivot = new Vector2(0.5f, 0f);
        sRect.anchoredPosition = new Vector2(0f, -200f);
        var staminaSlider = staminaObj.GetComponent<Slider>();
        if (playerStamina != null)
        {
            playerStamina.StaminaSlider = staminaSlider;
            var ui = staminaObj.AddComponent<StaminaBarUI>();
            ui.playerStamina = playerStamina;
        }
    }

    private void BuildPauseMenu()
    {
        pauseMenu = new GameObject("PauseMenu");
        pauseMenu.layer = LayerMask.NameToLayer("UI");
        pauseMenu.transform.SetParent(canvas.transform, false);
        var pmRect = pauseMenu.AddComponent<RectTransform>();
        pmRect.anchorMin = new Vector2(0.5f, 0.5f);
        pmRect.anchorMax = new Vector2(0.5f, 0.5f);
        pmRect.pivot = new Vector2(0.5f, 0.5f);
        pmRect.sizeDelta = new Vector2(400, 350);

        // Add a semi-transparent background
        var bg = pauseMenu.AddComponent<Image>();
        bg.color = new Color(0, 0, 0, 0.7f);

        var layout = pauseMenu.AddComponent<UnityEngine.UI.VerticalLayoutGroup>();
        layout.spacing = 20f;
        layout.padding.top = 25;
        layout.padding.bottom = 25;
        layout.childAlignment = TextAnchor.MiddleCenter;
        layout.childControlHeight = false;
        layout.childControlWidth = false;
        layout.childForceExpandHeight = false;
        layout.childForceExpandWidth = false;
        layout.childScaleHeight = false;
        layout.childScaleWidth = false;
        pauseMenu.AddComponent<UnityEngine.UI.ContentSizeFitter>().verticalFit = UnityEngine.UI.ContentSizeFitter.FitMode.PreferredSize;

        var saveBtn = CreateButton("Save", new Vector2(200, 50));
        saveBtn.layer = LayerMask.NameToLayer("UI");
        saveBtn.transform.SetParent(pauseMenu.transform, false);
        var saveRect = saveBtn.GetComponent<RectTransform>();
        saveRect.sizeDelta = new Vector2(200, 50);
        saveBtn.GetComponent<Button>().onClick.AddListener(() =>
        {
            Debug.Log("Save Button Clicked!");
            if (saveLoadManager != null)
                saveLoadManager.SaveGame();
        });

        var loadBtn = CreateButton("Load", new Vector2(200, 50));
        loadBtn.layer = LayerMask.NameToLayer("UI");
        loadBtn.transform.SetParent(pauseMenu.transform, false);
        var loadRect = loadBtn.GetComponent<RectTransform>();
        loadRect.sizeDelta = new Vector2(200, 50);
        loadBtn.GetComponent<Button>().onClick.AddListener(() =>
        {
            Debug.Log("Load Button Clicked!");
            if (saveLoadManager != null)
                saveLoadManager.LoadGame();
        });

        var resetBtn = CreateButton("Reset", new Vector2(200, 50));
        resetBtn.layer = LayerMask.NameToLayer("UI");
        resetBtn.transform.SetParent(pauseMenu.transform, false);
        var resetRect = resetBtn.GetComponent<RectTransform>();
        resetRect.sizeDelta = new Vector2(200, 50);
        resetBtn.GetComponent<Button>().onClick.AddListener(() =>
        {
            Debug.Log("Reset Button Clicked!");
            if (saveLoadManager != null)
                saveLoadManager.ResetSave();
        });
    }

    private GameObject CreateButton(string text, Vector2 size)
    {
        var go = new GameObject(text + "Button");
        var image = go.AddComponent<Image>();
        image.color = Color.white;
        var btn = go.AddComponent<Button>();

        var txtGO = new GameObject("Text");
        txtGO.transform.SetParent(go.transform, false);
        var txt = txtGO.AddComponent<Text>();
        txt.text = text;
        txt.alignment = TextAnchor.MiddleCenter;
        txt.color = Color.black;
        txt.font = Resources.GetBuiltinResource<Font>("LegacyRuntime.ttf");

        var rect = go.GetComponent<RectTransform>();
        rect.sizeDelta = size;

        var txtRect = txtGO.GetComponent<RectTransform>();
        txtRect.anchorMin = Vector2.zero;
        txtRect.anchorMax = Vector2.one;
        txtRect.offsetMin = Vector2.zero;
        txtRect.offsetMax = Vector2.zero;

        return go;
    }

    private GameObject CreateSlider(Vector2 size)
    {
        var go = new GameObject("StaminaBar");
        var slider = go.AddComponent<Slider>();
        slider.direction = Slider.Direction.LeftToRight;
        slider.minValue = 0f;
        slider.maxValue = 1f;
        var background = new GameObject("Background").AddComponent<Image>();
        background.color = Color.gray;
        background.transform.SetParent(go.transform, false);
        var fill = new GameObject("Fill").AddComponent<Image>();
        fill.color = Color.green;
        fill.transform.SetParent(background.transform, false);
        slider.targetGraphic = fill;
        slider.fillRect = fill.rectTransform;

        var rect = go.GetComponent<RectTransform>();
        rect.sizeDelta = size;

        background.rectTransform.anchorMin = Vector2.zero;
        background.rectTransform.anchorMax = Vector2.one;
        background.rectTransform.offsetMin = Vector2.zero;
        background.rectTransform.offsetMax = Vector2.zero;

        fill.rectTransform.anchorMin = Vector2.zero;
        fill.rectTransform.anchorMax = Vector2.one;
        fill.rectTransform.offsetMin = Vector2.zero;
        fill.rectTransform.offsetMax = Vector2.zero;

        return go;
    }

    public void TogglePause()
    {
        bool isActive = !pauseMenu.activeSelf;
        SetPauseState(isActive);
    }

    private void SetPauseState(bool paused)
    {
        pauseMenu.SetActive(paused);
        Time.timeScale = paused ? 0 : 1;

        // Cursor and input lock
        if (paused)
        {
            Cursor.lockState = CursorLockMode.None;
            Cursor.visible = true;
            // Disable player controls if possible
            var tpc = UnityEngine.Object.FindFirstObjectByType<ThirdPersonController>();
            if (tpc != null) tpc.enabled = false;
            var input = UnityEngine.Object.FindFirstObjectByType<StarterAssets.StarterAssetsInputs>();
            if (input != null) input.enabled = false;
        }
        else
        {
            Cursor.lockState = CursorLockMode.Locked;
            Cursor.visible = false;
            // Enable player controls if possible
            var tpc = UnityEngine.Object.FindFirstObjectByType<ThirdPersonController>();
            if (tpc != null) tpc.enabled = true;
            var input = UnityEngine.Object.FindFirstObjectByType<StarterAssets.StarterAssetsInputs>();
            if (input != null) input.enabled = true;
        }
    }
}
