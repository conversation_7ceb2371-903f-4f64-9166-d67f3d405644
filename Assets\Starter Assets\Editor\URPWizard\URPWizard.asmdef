{"name": "URPWizard", "rootNamespace": "", "references": ["Unity.RenderPipelines.Universal.Runtime"], "includePlatforms": ["Editor"], "excludePlatforms": [], "allowUnsafeCode": false, "overrideReferences": false, "precompiledReferences": [], "autoReferenced": true, "defineConstraints": [], "versionDefines": [{"name": "com.unity.render-pipelines.universal", "expression": "", "define": "USE_URP"}], "noEngineReferences": false}