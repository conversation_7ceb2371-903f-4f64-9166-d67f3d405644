using UnityEngine;
using UnityEngine.UI;

public class StaminaBarUI : MonoBehaviour
{
    public PlayerStamina playerStamina;
    public float hideDelay = 3f;

    private float timer;
    private Slider slider;
    private CanvasGroup canvasGroup;

    private void Awake()
    {
        slider = GetComponent<Slider>();
        canvasGroup = GetComponent<CanvasGroup>();
        if (canvasGroup == null)
            canvasGroup = gameObject.AddComponent<CanvasGroup>();
        // Always start visible if stamina is not full
        if (playerStamina != null && !Mathf.Approximately(playerStamina.Current, playerStamina.maxStamina))
        {
            ShowBar();
            timer = 0f;
        }
        else
        {
            HideBar();
            timer = 0f;
        }
    }

    private void Update()
    {
        if (playerStamina == null || slider == null) {
            // Optionally log once
            return;
        }
        // Show if stamina is not full and player is sprinting or recovering
        if (!Mathf.Approximately(playerStamina.Current, playerStamina.maxStamina))
        {
            timer = 0f;
            ShowBar();
        }
        else
        {
            timer += Time.unscaledDeltaTime;
            if (timer >= hideDelay)
            {
                HideBar();
            }
        }
    }
    private void ShowBar()
    {
        if (canvasGroup != null)
        {
            canvasGroup.alpha = 1f;
            canvasGroup.blocksRaycasts = true;
            canvasGroup.interactable = true;
        }
    }

    private void HideBar()
    {
        if (canvasGroup != null)
        {
            canvasGroup.alpha = 0f;
            canvasGroup.blocksRaycasts = false;
            canvasGroup.interactable = false;
        }
    }
}
