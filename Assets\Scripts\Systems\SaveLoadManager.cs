using System;
using System.IO;
using System.Collections.Generic;
using UnityEngine;
using StarterAssets;

[Serializable]
public struct PlacedObjectData
{
    public string prefabId;
    public Vector3 position;
    public Quaternion rotation;
}

[Serializable]
public class SaveData
{
    public Vector3 cameraPosition;
    public Vector3 playerPosition;
    public Quaternion playerRotation;
    public List<PlacedObjectData> placedObjects;
    // später z.B. Inventar, Stats, etc.
}

public class SaveLoadManager : MonoBehaviour
{
    private string saveFilePath;
    private Vector3 defaultPlayerPosition;
    private Quaternion defaultPlayerRotation;

    public void ResetSave()
    {
        if (File.Exists(saveFilePath))
            File.Delete(saveFilePath);

        foreach (var obj in UnityEngine.Object.FindObjectsByType<GameObject>(FindObjectsSortMode.None))
        {
            if (obj.layer == LayerMask.NameToLayer("PlacedBuilding"))
                Destroy(obj);
        }

        var player = UnityEngine.Object.FindFirstObjectByType<ThirdPersonController>();
        if (player != null)
        {
            var cc = player.GetComponent<CharacterController>();
            if (cc != null) cc.enabled = false;
            player.transform.SetPositionAndRotation(defaultPlayerPosition, defaultPlayerRotation);
            if (cc != null) cc.enabled = true;
        }
    }

    private void Awake()
    {
        saveFilePath = Path.Combine(Application.persistentDataPath, "savegame.json");
        var player = UnityEngine.Object.FindFirstObjectByType<ThirdPersonController>();
        if (player != null)
        {
            defaultPlayerPosition = player.transform.position;
            defaultPlayerRotation = player.transform.rotation;
        }
        LoadGame();
    }

    private void OnApplicationQuit()
    {
        SaveGame();
    }

    public void SaveGame()
    {
        var placedObjects = new List<PlacedObjectData>();

        foreach (var obj in UnityEngine.Object.FindObjectsByType<GameObject>(FindObjectsSortMode.None))
        {
            if (obj.layer != LayerMask.NameToLayer("PlacedBuilding"))
                continue;

            placedObjects.Add(new PlacedObjectData
            {
                prefabId = obj.name.Replace("(Clone)", string.Empty),
                position = obj.transform.position,
                rotation = obj.transform.rotation
            });
        }

        var player = UnityEngine.Object.FindFirstObjectByType<ThirdPersonController>();
        Vector3 pPos = player != null ? player.transform.position : defaultPlayerPosition;
        Quaternion pRot = player != null ? player.transform.rotation : defaultPlayerRotation;

        var data = new SaveData
        {
            cameraPosition = Camera.main.transform.position,
            playerPosition = pPos,
            playerRotation = pRot,
            placedObjects = placedObjects
        };

        try
        {
            string json = JsonUtility.ToJson(data, prettyPrint: true);
            File.WriteAllText(saveFilePath, json);
        }
        catch (Exception e)
        {
            Debug.LogError("Failed to save game: " + e);
        }
    }

    public void LoadGame()
    {
        if (!File.Exists(saveFilePath)) return;

        try
        {
            string json = File.ReadAllText(saveFilePath);
            var data = JsonUtility.FromJson<SaveData>(json);
            Camera.main.transform.position = data.cameraPosition;
            var player = UnityEngine.Object.FindFirstObjectByType<ThirdPersonController>();
            if (player != null)
            {
                var cc = player.GetComponent<CharacterController>();
                if (cc != null) cc.enabled = false;
                player.transform.SetPositionAndRotation(data.playerPosition, data.playerRotation);
                if (cc != null) cc.enabled = true;
            }

            // Remove existing placed objects before loading
            foreach (var obj in UnityEngine.Object.FindObjectsByType<GameObject>(FindObjectsSortMode.None))
            {
                if (obj.layer == LayerMask.NameToLayer("PlacedBuilding"))
                    Destroy(obj);
            }

            if (data.placedObjects != null)
            {
                foreach (var pod in data.placedObjects)
                {
                    GameObject prefab = Resources.Load<GameObject>(pod.prefabId);
                    GameObject obj;

                    if (prefab != null)
                    {
                        obj = Instantiate(prefab, pod.position, pod.rotation);
                    }
                    else
                    {
                        obj = GameObject.CreatePrimitive(PrimitiveType.Cube);
                        obj.transform.position = pod.position;
                        obj.transform.rotation = pod.rotation;
                        obj.name = pod.prefabId;
                    }

                    obj.layer = LayerMask.NameToLayer("PlacedBuilding");
                }
            }
        }
        catch (Exception e)
        {
            Debug.LogError("Failed to load game: " + e);
        }
    }
}
