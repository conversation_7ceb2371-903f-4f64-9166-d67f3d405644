using UnityEngine;

public class PlacementGhost : MonoBehaviour
{
    private Renderer[] renderers;
    private Material validMaterial;
    private Material invalidMaterial;

    private Vector3 boundsOffset;
    private Vector3 boundsSize;

    public bool IsValid { get; private set; } = true;

    public Bounds Bounds
    {
        get
        {
            if (renderers != null && renderers.Length > 0)
            {
                var b = renderers[0].bounds;
                for (int i = 1; i < renderers.Length; i++)
                    b.Encapsulate(renderers[i].bounds);
                boundsSize = b.size;
                boundsOffset = b.center - transform.position;
            }

            return new Bounds(transform.position + boundsOffset, boundsSize);
        }
    }

    public void Init(Material validMat, Material invalidMat)
    {
        renderers = GetComponentsInChildren<Renderer>();
        validMaterial = validMat;
        invalidMaterial = invalidMat;

        if (renderers.Length > 0)
        {
            var combined = renderers[0].bounds;
            foreach (var r in renderers)
            {
                r.sharedMaterial = validMaterial;
                combined.Encapsulate(r.bounds);
            }

            boundsSize = combined.size;
            boundsOffset = combined.center - transform.position;
        }
        else
        {
            boundsSize = Vector3.one;
            boundsOffset = Vector3.zero;
        }
    }

    public void SetValid(bool valid)
    {
        IsValid = valid;
        if (renderers == null) return;
        foreach (var r in renderers)
        {
            r.sharedMaterial = valid ? validMaterial : invalidMaterial;
        }
    }
}
