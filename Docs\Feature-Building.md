# Feature-Building

Diese Datei beschreibt den aktuellen Stand des Basisbau-Systems nach dem Pivot auf freies Platzieren.

---

## <PERSON><PERSON><PERSON> von G<PERSON> zu Freiform

Das erste Prototype setzte auf ein starres Raster (`GridManager`, `GhostController` usw.). Dieses System wurde entfernt, da es zu unflexibel war. Zukünftig können Objekte ohne Snap an beliebigen Positionen platziert werden.

### Aktueller Stand
* Aktiviere Placement-Mode mit **B**.
* `PlacementController` verwaltet die Platzierungslogik. Ein `PlacementGhost` verfolgt den Mauszeiger und signalisiert mit grün/rot, ob das Objekt gesetzt werden kann.
* Halte **R** und drehe mit dem Mausrad das Vorschauobjekt stufenlos.
* Nahe Platzierungen schnappen an bereits gebaute Objekte an und richten sich automatisch an der nächstgelegenen Oberfläche (oben, unten oder seitlich) aus.
* Ghost-Colliders sind deaktiviert und liegen im Layer `Ignore Raycast`, damit Objekt-Löschen per Rechtsklick zuverlässig funktioniert.
* Wechseln zum Delete-Mode mit **Delete** während Placement-Mode aktiv ist.
* Rechtsklick im Delete-Mode löscht das aktuell rot hervorgehobene Objekt.
* Kollisionsprüfung erfolgt über eine leicht verkleinerte Bounding Box, um flackernde Ungültig-States zu vermeiden. Geprüft wird nur gegen Objekte im Layer `PlacedBuilding`.
* Platzierte Objekte landen im Layer `PlacedBuilding` und werden beim Speichern vom `SaveLoadManager` (JSON) persistiert. Ein Reset-Button kann das Savegame löschen und die Szene leeren.
* Eine dedizierte Test-Szene fehlt noch, kann aber leicht selbst erstellt werden.

---

## First-Person Controller & Kamera

Der Prototyp nutzt das **PlayerArmature**-Prefab aus den Unity Starter Assets. Darin enthalten sind `ThirdPersonController` und `StarterAssetsInputs`. Das Kindobjekt `CinemachineCameraTarget` dient als Follow- und LookAt-Ziel einer Cinemachine Virtual Camera.

1. `Starter Assets/Runtime/ThirdPersonController/Prefabs/PlayerArmature.prefab` in die Szene ziehen.
2. Eine Cinemachine Virtual Camera erstellen und bei **Follow** `CinemachineCameraTarget` zuweisen.
3. Die eigentliche Hauptkamera wird durch die Virtual Camera gesteuert und kann frei in der Szene platziert werden.
4. Bewegungsgeschwindigkeit, Sprunghöhe usw. lassen sich direkt im `ThirdPersonController` anpassen.

## UI-Interaktion & Mauszeiger

Das HUD stellt Buttons für **Save**, **Load** und **Reset** bereit. Für die Bedienung muss der Mauszeiger entsperrt werden:

```csharp
var input = FindObjectOfType<StarterAssetsInputs>();
input.cursorLocked = false;
input.cursorInputForLook = false;
Cursor.lockState = CursorLockMode.None;
```

Nach Schließen des UI dieselben Werte wieder auf `true` setzen und den Cursor mit `CursorLockMode.Locked` sperren, damit die Kamera erneut der Maus folgt.

---

*Stand: Sprint 1, Pivot*
