// Placement Controller

using UnityEngine;

public class PlacementController : MonoBehaviour
{
    [Tooltip("Prefabs that can be placed")] public GameObject[] placeablePrefabs;
    [Tooltip("Material used when placement position is valid")] public Material ghostValidMaterial;
    [Tooltip("Material used when placement position is invalid")] public Material ghostInvalidMaterial;
    [Tooltip("Color used to highlight deletable objects")] public Color deleteHighlightColor = new Color(1f, 0.6f, 0.6f, 1f);
    [Tooltip("Layers considered when checking for placement collisions")] public LayerMask collisionLayers = 0;
    [Tooltip("Layers that the placement raycast can hit")] public LayerMask placementLayers = ~0;
    [Tooltip("Distance within which placement snaps to existing objects")] public float snapRange = 1f;
    [Tooltip("Degrees rotated per mouse scroll step while holding R")] public float rotationSpeed = 90f;
    [Tooltip("Margin used when checking collisions to avoid false positives")] public float collisionMargin = 0.02f;

    [Tooltip("Key to toggle delete mode")] public KeyCode toggleDeleteModeKey = KeyCode.Delete;

    [Toolt<PERSON>("Is placement mode currently active")] public bool isPlacementActive = true;


    private PlacementGhost ghost;
    private GameObject ghostInstance;
    private int currentIndex;

    private Camera mainCam;
    private GameObject deleteTarget;
    private Renderer[] deleteRenderers;
    private Color[] originalColors;
    private bool deleteMode;
    private LayerMask placedBuildingMask;

    private void Start()
    {
        mainCam = Camera.main;
        if (collisionLayers == 0)
            collisionLayers = LayerMask.GetMask("PlacedBuilding");
        if (placementLayers == ~0)
            placementLayers = ~LayerMask.GetMask("Ignore Raycast");
        placedBuildingMask = LayerMask.GetMask("PlacedBuilding");
    }

    private void Update()
    {
        if (placeablePrefabs == null || placeablePrefabs.Length == 0)
            return;

        if (!isPlacementActive)
        {
            if (ghostInstance != null)
                Destroy(ghostInstance);
            return;
        }

        if (Input.GetKeyDown(KeyCode.Alpha1)) SetPrefabIndex(0);
        if (Input.GetKeyDown(KeyCode.Alpha2) && placeablePrefabs.Length > 1) SetPrefabIndex(1);

        if (Input.GetKeyDown(toggleDeleteModeKey))
            ToggleDeleteMode();

        if (deleteMode)
        {
            UpdateDeleteHighlight();
            if (Input.GetMouseButtonDown(0) && deleteTarget != null)
            {
                GameObject target = deleteTarget;
                ClearDeleteHighlight();
                Destroy(target);
            }
            return;
        }

        if (ghostInstance == null)
        {
            SpawnGhost();
            return;
        }

        HandleRotationInput();
        UpdateGhostPosition();

        if (Input.GetMouseButtonDown(0) && ghost != null && ghost.IsValid)
        {
            PlaceObject();
        }
    }

    private void SetPrefabIndex(int index)
    {
        if (index >= placeablePrefabs.Length) return;
        currentIndex = index;
        if (ghostInstance != null) Destroy(ghostInstance);
        SpawnGhost();
    }

    private void SpawnGhost()
    {
        ghostInstance = Instantiate(placeablePrefabs[currentIndex]);
        foreach (var col in ghostInstance.GetComponentsInChildren<Collider>())
            col.enabled = false;
        SetLayerRecursively(ghostInstance, LayerMask.NameToLayer("Ignore Raycast"));
        ghost = ghostInstance.AddComponent<PlacementGhost>();
        ghost.Init(ghostValidMaterial, ghostInvalidMaterial);
    }

    private void UpdateGhostPosition()
    {
        Ray ray = mainCam.ScreenPointToRay(Input.mousePosition);
        if (Physics.Raycast(ray, out RaycastHit hit, 100f, placementLayers))
        {
            Bounds bounds = ghost.Bounds;
            Vector3 targetPos = hit.point + Vector3.up * bounds.extents.y;

            Collider[] snapColliders = Physics.OverlapSphere(targetPos, snapRange, LayerMask.GetMask("PlacedBuilding"));
            if (snapColliders.Length > 0)
            {
                Collider nearest = snapColliders[0];
                float minDist = Vector3.Distance(targetPos, nearest.bounds.center);
                foreach (var c in snapColliders)
                {
                    float d = Vector3.Distance(targetPos, c.bounds.center);
                    if (d < minDist)
                    {
                        nearest = c;
                        minDist = d;
                    }
                }

                Bounds nb = nearest.bounds;
                Vector3 delta = targetPos - nb.center;
                Vector3 offset = Vector3.zero;
                Vector3 ghostExt = bounds.extents;

                if (Mathf.Abs(delta.x) >= Mathf.Abs(delta.y) && Mathf.Abs(delta.x) >= Mathf.Abs(delta.z))
                {
                    offset = new Vector3(Mathf.Sign(delta.x) * (nb.extents.x + ghostExt.x), 0f, 0f);
                }
                else if (Mathf.Abs(delta.y) >= Mathf.Abs(delta.x) && Mathf.Abs(delta.y) >= Mathf.Abs(delta.z))
                {
                    offset = new Vector3(0f, Mathf.Sign(delta.y) * (nb.extents.y + ghostExt.y), 0f);
                }
                else
                {
                    offset = new Vector3(0f, 0f, Mathf.Sign(delta.z) * (nb.extents.z + ghostExt.z));
                }

                targetPos = nb.center + offset;
            }

            ghostInstance.transform.position = targetPos;
            bounds = ghost.Bounds;
            Vector3 checkExt = bounds.extents - Vector3.one * collisionMargin;
            checkExt = Vector3.Max(checkExt, Vector3.zero);
            Collider[] overlap = Physics.OverlapBox(bounds.center, checkExt, ghostInstance.transform.rotation, collisionLayers);
            bool isValid = overlap.Length == 0;

            if (isValid)
            {
                if (snapColliders.Length > 0)
                {
                    Collider nearest = snapColliders[0];
                    float minDist = Vector3.Distance(targetPos, nearest.bounds.center);
                    foreach (var c in snapColliders)
                    {
                        float d = Vector3.Distance(targetPos, c.bounds.center);
                        if (d < minDist)
                        {
                            nearest = c;
                            minDist = d;
                        }
                    }

                    Bounds nb = nearest.bounds;
                    Vector3 delta = targetPos - nb.center;
                    Vector3 offset = Vector3.zero;
                    Vector3 ghostExt = bounds.extents;

                    if (Mathf.Abs(delta.x) >= Mathf.Abs(delta.y) && Mathf.Abs(delta.x) >= Mathf.Abs(delta.z))
                    {
                        offset = new Vector3(Mathf.Sign(delta.x) * (nb.extents.x + ghostExt.x), 0f, 0f);
                    }
                    else if (Mathf.Abs(delta.y) >= Mathf.Abs(delta.x) && Mathf.Abs(delta.y) >= Mathf.Abs(delta.z))
                    {
                        offset = new Vector3(0f, Mathf.Sign(delta.y) * (nb.extents.y + ghostExt.y), 0f);
                    }
                    else
                    {
                        offset = new Vector3(0f, 0f, Mathf.Sign(delta.z) * (nb.extents.z + ghostExt.z));
                    }

                    targetPos = nb.center + offset;
                }
            }

            ghostInstance.transform.position = targetPos;
            ghost.SetValid(isValid);
        }
    }

    private void PlaceObject()
    {
        GameObject placed = Instantiate(placeablePrefabs[currentIndex], ghostInstance.transform.position,
            ghostInstance.transform.rotation);
        SetLayerRecursively(placed, LayerMask.NameToLayer("PlacedBuilding"));
        ClearDeleteHighlight();
    }

    private void HandleRotationInput()
    {
        if (ghostInstance == null) return;
        if (!Input.GetKey(KeyCode.R)) return;

        float scroll = Input.mouseScrollDelta.y;
        if (Mathf.Abs(scroll) > 0.01f)
        {
            ghostInstance.transform.Rotate(Vector3.up, scroll * rotationSpeed, Space.World);
        }
    }

    private void UpdateDeleteHighlight()
    {
        Ray ray = mainCam.ScreenPointToRay(Input.mousePosition);
        if (Physics.Raycast(ray, out RaycastHit hit, 100f, placedBuildingMask))
        {
            var obj = hit.collider.gameObject;
            if (obj.layer == LayerMask.NameToLayer("PlacedBuilding"))
            {
                if (deleteTarget != obj)
                {
                    ClearDeleteHighlight();
                    deleteTarget = obj;
                    deleteRenderers = deleteTarget.GetComponentsInChildren<Renderer>();
                    originalColors = new Color[deleteRenderers.Length];
                    for (int i = 0; i < deleteRenderers.Length; i++)
                    {
                        originalColors[i] = deleteRenderers[i].material.color;
                        deleteRenderers[i].material.color = deleteHighlightColor;
                    }
                }
                return;
            }
        }

        ClearDeleteHighlight();
    }

    private void ClearDeleteHighlight()
    {
        if (deleteRenderers != null)
        {
            for (int i = 0; i < deleteRenderers.Length; i++)
            {
                if (deleteRenderers[i] != null)
                    deleteRenderers[i].material.color = originalColors[i];
            }
        }

        deleteTarget = null;
        deleteRenderers = null;
        originalColors = null;
    }

    private void ToggleDeleteMode()
    {
        deleteMode = !deleteMode;
        if (ghostInstance != null)
            ghostInstance.SetActive(!deleteMode);
        if (!deleteMode)
            ClearDeleteHighlight();
    }

    public void TogglePlacementMode()
    {
        isPlacementActive = !isPlacementActive;

        if (!isPlacementActive && ghostInstance != null)
        {
            Destroy(ghostInstance);
        }
        else if (isPlacementActive && ghostInstance == null && !deleteMode)
        {
            SpawnGhost();
        }
    }

    private void SetLayerRecursively(GameObject obj, int layer)
    {
        obj.layer = layer;
        foreach (Transform child in obj.transform)
        {
            SetLayerRecursively(child.gameObject, layer);
        }
    }
}
