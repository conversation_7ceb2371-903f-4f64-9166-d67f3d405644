using UnityEngine;
using UnityEngine.UI;
using StarterAssets;

/// <summary>
/// Simple stamina system for sprinting.
/// </summary>
[RequireComponent(typeof(StarterAssetsInputs))]
public class PlayerStamina : MonoBehaviour
{
    public float maxStamina = 5f;
    public float regenRate = 1f;
    public float sprintDrain = 1f;
    public Slider StaminaSlider;

    private StarterAssetsInputs _input;
    private float _current;

    public float Current => _current;

    private void Start()
    {
        _input = GetComponent<StarterAssetsInputs>();
        _current = maxStamina;
        if (StaminaSlider != null)
        {
            StaminaSlider.minValue = 0f;
            StaminaSlider.maxValue = maxStamina;
            StaminaSlider.value = _current;
        }
    }

    private void Update()
    {
        if (_input == null) return;

        if (_input.sprint && _input.move != Vector2.zero)
        {
            _current -= sprintDrain * Time.deltaTime;
            if (_current <= 0f)
            {
                _current = 0f;
                _input.sprint = false;
            }
        }
        else
        {
            _current += regenRate * Time.deltaTime;
        }

        _current = Mathf.Clamp(_current, 0f, maxStamina);

        if (StaminaSlider != null)
            StaminaSlider.value = _current;
    }
}
